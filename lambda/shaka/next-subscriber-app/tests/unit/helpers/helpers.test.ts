import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  formatPortingCode,
  getNextWorkingDay,
  getAvailableDatesRange,
  getStepFromPath,
  createStepRoutes,
  flattenValidationErrors,
  selectAvailableId,
  availableMainPlanIds,
  availableFamilyPlanIds,
  checkIsSafari,
  checkIsAndroid,
  checkIsIos,
  checkIsDesktop,
  standariseNetworkError,
  createTestId,
  getRemainingDays,
  constructRemainingDaysMessage,
  calculateTotalCost,
  getFieldError,
  hasAlreadyRestoredSignupStep,
  isValidSignupPath,
  shouldRestoreToSavedPath,
  markSignupStepAsRestored,
  getCurrentSignupStep,
  extractCorrelationIdsFromBasket,
  findSimsByCorrelationIds,
  filterSubscriptionsByCorrelationIds
} from '@/utils/helpers';
import type {
  ValidationErrorObject,
  OrderItemWithQuantity
} from '@/utils/helpers';
import { validateNumberPortingForm } from '@/schema/schema';
import { UK_PUBLIC_HOLIDAYS } from '@/utils/constants';
import { getSignupPageName } from '@/src/uswitch/utils/helpers';
import { getDayMonthYearFromDate } from '@/utils/formatters';
import { LocalKey } from '@/hooks/useLocalStorage';
import { filterActionGroups } from '@/src/deliveroo/utils/helpers';

const getDateString = (date: Date) => date.toISOString().slice(0, 10);

// Mock localStorage and sessionStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};

const mockSessionStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
};

// Mock window object
const mockWindow = {
  navigator: {
    userAgent: ''
  }
};

beforeEach(() => {
  vi.stubGlobal('localStorage', mockLocalStorage);
  vi.stubGlobal('sessionStorage', mockSessionStorage);
  vi.stubGlobal('window', mockWindow);

  // Reset all mocks
  vi.clearAllMocks();
});

afterEach(() => {
  vi.unstubAllGlobals();
});

describe('formatPortingCode', () => {
  it('removes spaces and converts to uppercase', () => {
    expect(formatPortingCode('pac 123 456 789')).toBe('PAC123456789');
    expect(formatPortingCode('pac1 23 456 78 9')).toBe('PAC123456789');
  });

  describe('plan reconciliation helpers', () => {
    describe('selectAvailableId', () => {
      it('returns storedId when it is available', () => {
        expect(selectAvailableId(5, [1, 5, 9])).toBe(5);
      });

      it('returns fallbackId when storedId is invalid and fallbackId is available', () => {
        expect(selectAvailableId(10, [2, 3, 4], 3)).toBe(3);
      });

      it('returns first available when neither storedId nor fallbackId are available', () => {
        expect(selectAvailableId(10, [7, 8, 9], 99)).toBe(7);
      });

      it('returns storedId when availableIds is empty', () => {
        expect(selectAvailableId(42, [], 1)).toBe(42);
      });

      it('returns fallbackId when storedId is undefined and availableIds is empty', () => {
        expect(selectAvailableId(undefined, [], 11)).toBe(11);
      });

      it('returns first available when storedId is undefined and list is non-empty', () => {
        expect(selectAvailableId(undefined, [4, 5, 6], 1)).toBe(4);
      });
    });

    describe('availableMainPlanIds', () => {
      it('extracts ids from planData', () => {
        const planData = [{ id: 4 }, { id: 5 }] as any;
        expect(availableMainPlanIds(planData)).toEqual([4, 5]);
      });

      it('returns empty array for undefined or empty input', () => {
        expect(availableMainPlanIds(undefined)).toEqual([]);
        expect(availableMainPlanIds([] as any)).toEqual([]);
      });
    });

    describe('availableFamilyPlanIds', () => {
      it('extracts family plan ids from nested planData', () => {
        const planData = [
          { id: 1, family_plans: [{ id: 10 }, { id: 11 }] },
          { id: 2, family_plans: [{ id: 20 }] }
        ] as any;
        expect(availableFamilyPlanIds(planData)).toEqual([10, 11, 20]);
      });

      it('handles missing or empty family_plans arrays', () => {
        const planData = [{ id: 1 }, { id: 2, family_plans: [] }] as any;
        expect(availableFamilyPlanIds(planData)).toEqual([]);
      });

      it('returns empty array for undefined input', () => {
        expect(availableFamilyPlanIds(undefined)).toEqual([]);
      });
    });
  });

  it('handles already formatted codes', () => {
    expect(formatPortingCode('PAC123456789')).toBe('PAC123456789');
  });

  it('handles mixed case input', () => {
    expect(formatPortingCode('PaC123456789')).toBe('PAC123456789');
  });

  it('returns original if format is invalid', () => {
    // Too short
    expect(formatPortingCode('PAC123')).toBe('PAC123');
    // Wrong prefix
    expect(formatPortingCode('ABC123456789')).toBe('ABC123456789');
    // Contains non-alphanumeric
    expect(formatPortingCode('PAC-123-456-789')).toBe('PAC-123-456-789');
  });

  it('handles empty string', () => {
    expect(formatPortingCode('')).toBe('');
  });
});

describe('getNextWorkingDay', () => {
  it('returns the next weekday if no weekend or holiday', () => {
    const result = getNextWorkingDay(
      new Date('2025-12-24'),
      UK_PUBLIC_HOLIDAYS
    );
    expect(getDateString(result)).toBe('2025-12-29'); // 25th and 26th are holidays, 27th/28th are weekend, 29th is Monday
  });

  it('skips weekends', () => {
    const result = getNextWorkingDay(
      new Date('2025-12-19'),
      UK_PUBLIC_HOLIDAYS
    ); // Friday
    expect(getDateString(result)).toBe('2025-12-22'); // Monday
  });

  it('skips holidays', () => {
    const result = getNextWorkingDay(
      new Date('2025-12-24'),
      UK_PUBLIC_HOLIDAYS
    );
    expect(getDateString(result)).toBe('2025-12-29'); // 25th, 26th are holidays, 27th/28th are weekend
  });

  it('returns the next working day if input is a holiday', () => {
    const result = getNextWorkingDay(
      new Date('2025-12-25'),
      UK_PUBLIC_HOLIDAYS
    );
    expect(getDateString(result)).toBe('2025-12-29');
  });

  it('returns the next working day if input is a weekend', () => {
    const result = getNextWorkingDay(
      new Date('2025-12-27'),
      UK_PUBLIC_HOLIDAYS
    ); // Saturday
    expect(getDateString(result)).toBe('2025-12-29'); // Monday
  });
});

describe('getAvailableDatesRange', () => {
  it('returns the correct number of working days', () => {
    const result = getAvailableDatesRange(
      new Date('2025-12-22'),
      5,
      UK_PUBLIC_HOLIDAYS
    );
    expect(result).toHaveLength(5);
  });

  it('skips weekends and holidays', () => {
    const result = getAvailableDatesRange(
      new Date('2025-12-24'),
      3,
      UK_PUBLIC_HOLIDAYS
    );
    const isoDates = result.map(getDateString);
    expect(isoDates).toEqual(['2025-12-29', '2025-12-30', '2025-12-31']);
  });

  it('returns empty array if 0 days requested', () => {
    const result = getAvailableDatesRange(
      new Date('2025-12-22'),
      0,
      UK_PUBLIC_HOLIDAYS
    );
    expect(result).toEqual([]);
  });

  it('works with no holidays', () => {
    const result = getAvailableDatesRange(new Date('2025-12-22'), 2);
    const isoDates = result.map(getDateString);
    expect(isoDates).toEqual(['2025-12-23', '2025-12-24']);
  });
});

describe('validateNumberPortingForm', () => {
  it('validates a correct form (no date)', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '***********',
      desired_date: '2025-08-07'
    });
    expect(result.success).toBe(true);
    expect(result.data).toMatchObject({
      pacCode: 'PAC123456',
      incoming_phone_number: '447123456789',
      desired_date: '2025-08-07'
    });
  });

  it('validates a correct form (with valid business day date)', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '+447123456789',
      day: '15',
      month: '6',
      year: '2025',
      desired_date: '2025-06-15'
    });

    const validResult = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '+447123456789',
      day: '16',
      month: '6',
      year: '2025',
      desired_date: '2025-06-16'
    });

    expect(validResult.success).toBe(true);
    expect(validResult.data).toMatchObject({
      pacCode: 'PAC123456',
      incoming_phone_number: '447123456789',
      day: '16',
      month: '6',
      year: '2025'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(
      // @ts-expect-error tests
      result.error.issues.some((i) => i.message.includes('business day'))
    ).toBe(true);
  });

  it('fails on invalid PAC code', () => {
    const result = validateNumberPortingForm({
      pacCode: '123456',
      incoming_phone_number: '***********'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    // @ts-expect-error tests
    expect(result.error.issues.some((i) => i.path.includes('pacCode'))).toBe(
      true
    );
  });

  it('fails on invalid phone number', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '***********' // landline
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(
      // @ts-expect-error tests
      result.error.issues.some((i) => i.path.includes('incoming_phone_number'))
    ).toBe(true);
  });

  it('fails if only some date fields are present', () => {
    const missingDay = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '***********',
      month: '2',
      year: '2025',
      desired_date: '2025-06-15'
    });
    expect(missingDay.success).toBe(false);
    // @ts-expect-error tests
    expect(missingDay.error.issues.some((i) => i.path.includes('day'))).toBe(
      true
    );

    const missingMonth = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '***********',
      day: '1',
      year: '2025',
      desired_date: '2025-06-15'
    });
    expect(missingMonth.success).toBe(false);
    expect(
      // @ts-expect-error tests
      missingMonth.error.issues.some((i) => i.path.includes('month'))
    ).toBe(true);

    const missingYear = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '***********',
      day: '1',
      month: '2',
      desired_date: '2025-06-15'
    });
    expect(missingYear.success).toBe(false);
    // @ts-expect-error tests
    expect(missingYear.error.issues.some((i) => i.path.includes('year'))).toBe(
      true
    );
  });

  it('fails on invalid date (e.g., Feb 30)', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '***********',
      day: '30',
      month: '2',
      year: '2025',
      desired_date: '2025-02-30'
    });
    expect(result.success).toBe(false);
    expect(
      // @ts-expect-error tests
      result.error.issues.some((i) => i.message.includes('valid date'))
    ).toBe(true);
  });

  it('accepts empty date fields as undefined', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '***********',
      day: '',
      month: '',
      year: '',
      desired_date: '2025-06-15'
    });
    expect(result.success).toBe(true);
    expect(result.data).toMatchObject({
      pacCode: 'PAC123456',
      incoming_phone_number: '447123456789'
    });
  });

  it('fails on extra fields (should ignore or error)', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '***********',
      foo: 'bar',
      desired_date: '2025-06-15'
    });
    expect(result.success).toBe(true);
    expect(result.data).not.toHaveProperty('foo');
  });

  it('fails gracefully on null/undefined/empty input', () => {
    expect(validateNumberPortingForm(null).success).toBe(false);
    expect(validateNumberPortingForm(undefined).success).toBe(false);
    expect(validateNumberPortingForm({}).success).toBe(false);
  });

  it('fails if code is more than 9 characters', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC1234567', // 10 chars
      incoming_phone_number: '***********'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(result.error?.issues.some((i) => i.path.includes('pacCode'))).toBe(
      true
    );
  });

  it('fails if code is less than 9 characters', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC12345', // 8 chars
      incoming_phone_number: '***********'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(result.error?.issues.some((i) => i.path.includes('pacCode'))).toBe(
      true
    );
  });

  it('fails if code has special characters', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC!23456',
      incoming_phone_number: '***********'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(result.error?.issues.some((i) => i.path.includes('pacCode'))).toBe(
      true
    );
  });

  it('fails if code has spaces', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC 123456',
      phoneNumber: '***********'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(result.error?.issues.some((i) => i.path.includes('pacCode'))).toBe(
      true
    );
  });

  it('fails if code is all digits', () => {
    const result = validateNumberPortingForm({
      pacCode: '123456789',
      incoming_phone_number: '***********'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(result.error?.issues.some((i) => i.path.includes('pacCode'))).toBe(
      true
    );
  });

  it('fails if code is all letters', () => {
    const result = validateNumberPortingForm({
      pacCode: 'ABCDEFGHI',
      incoming_phone_number: '***********'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(result.error?.issues.some((i) => i.path.includes('pacCode'))).toBe(
      true
    );
  });

  it('fails if code is blank or whitespace', () => {
    const result = validateNumberPortingForm({
      pacCode: '   ',
      incoming_phone_number: '***********'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(result.error?.issues.some((i) => i.path.includes('pacCode'))).toBe(
      true
    );
  });

  it('fails if phone number contains letters', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '07123abc789'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(
      result.error?.issues.some((i) => i.path.includes('incoming_phone_number'))
    ).toBe(true);
  });

  it('fails if phone number is too short', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '07123'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(
      result.error?.issues.some((i) => i.path.includes('incoming_phone_number'))
    ).toBe(true);
  });

  it('fails if date fields are non-numeric', () => {
    const result = validateNumberPortingForm({
      pacCode: 'PAC123456',
      incoming_phone_number: '***********',
      day: 'xx',
      month: '6',
      year: '2025',
      desired_date: ''
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(
      result.error?.issues.some((i) => i.message.includes('valid date'))
    ).toBe(true);
  });

  it('fails if all fields are empty', () => {
    const result = validateNumberPortingForm({
      pacCode: '',
      incoming_phone_number: '',
      day: '',
      month: '',
      year: ''
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
  });

  it('fails if code and phone number are both invalid', () => {
    const result = validateNumberPortingForm({
      pacCode: '123',
      incoming_phone_number: 'abc'
    });
    expect(result.success).toBe(false);
    expect(result.error).toBeDefined();
    expect(result.error?.issues.some((i) => i.path.includes('pacCode'))).toBe(
      true
    );
    expect(
      result.error?.issues.some((i) => i.path.includes('incoming_phone_number'))
    ).toBe(true);
  });
});

describe('getSignupPageName', () => {
  it('extracts and formats the page name from pathname', () => {
    expect(getSignupPageName('/signup/plan-selection')).toBe('Plan Selection');
    expect(getSignupPageName('/signup/add-ons')).toBe('Add Ons');
    expect(getSignupPageName('/signup/number-porting')).toBe('Number Porting');
  });

  it('handles underscores and hyphens', () => {
    expect(getSignupPageName('/signup/plan_selection')).toBe('Plan Selection');
    expect(getSignupPageName('/signup/plan-selection_extra')).toBe(
      'Plan Selection Extra'
    );
  });

  it('returns empty string if no segment after signup', () => {
    expect(getSignupPageName('/signup/')).toBe('');
    expect(getSignupPageName('/signup')).toBe('');
  });

  it('ignores query and hash', () => {
    expect(getSignupPageName('/signup/plan-selection?foo=bar')).toBe(
      'Plan Selection'
    );
    expect(getSignupPageName('/signup/plan-selection#section')).toBe(
      'Plan Selection'
    );
  });

  it('returns empty string for unrelated routes', () => {
    expect(getSignupPageName('/dashboard')).toBe('');
    expect(getSignupPageName('/')).toBe('');
  });

  it('returns empty string for invalid or non-string inputs', () => {
    // @ts-expect-error test
    expect(getSignupPageName(null)).toBe('');
    // @ts-expect-error test
    expect(getSignupPageName(undefined)).toBe('');
    // @ts-expect-error test
    expect(getSignupPageName(123)).toBe('');
    expect(getSignupPageName('')).toBe('');
  });
});

describe('getStepFromPath', () => {
  it('should return the correct step key for matching pathname', () => {
    const stepRoutes = {
      step1: 'profile',
      step2: 'settings',
      step3: 'billing'
    };

    const result = getStepFromPath(stepRoutes, '/account/profile');
    expect(result).toBe('step1');
  });

  it('should handle multi-segment paths correctly', () => {
    const stepRoutes = {
      dashboard: 'overview',
      settings: 'preferences',
      profile: 'edit'
    };

    const result = getStepFromPath(stepRoutes, '/app/user/account/preferences');
    expect(result).toBe('settings');
  });

  it('should handle various route configurations', () => {
    const stepRoutes = {
      'onboarding-step-1': 'welcome',
      user_profile: 'profile-edit',
      'final-step': 'complete'
    };

    const result1 = getStepFromPath(stepRoutes, '/onboarding/welcome');
    const result2 = getStepFromPath(stepRoutes, '/user/profile-edit');
    const result3 = getStepFromPath(stepRoutes, '/flow/complete');

    expect(result1).toBe('onboarding-step-1');
    expect(result2).toBe('user_profile');
    expect(result3).toBe('final-step');
  });

  it('should return undefined for null or undefined pathname', () => {
    const stepRoutes = {
      step1: 'profile',
      step2: 'settings'
    };

    const result1 = getStepFromPath(stepRoutes, null);
    // @ts-expect-error test
    const result2 = getStepFromPath(stepRoutes, undefined);

    expect(result1).toBeUndefined();
    expect(result2).toBeUndefined();
  });

  it('should return undefined for unmatched pathname', () => {
    const stepRoutes = {
      step1: 'profile',
      step2: 'settings'
    };

    const result = getStepFromPath(stepRoutes, '/account/nonexistent');
    expect(result).toBeUndefined();
  });

  it('should handle empty and root path scenarios', () => {
    const stepRoutes = {
      home: '',
      step1: 'profile'
    };

    const result1 = getStepFromPath(stepRoutes, '');
    const result2 = getStepFromPath(stepRoutes, '/');
    const result3 = getStepFromPath(stepRoutes, '///');

    expect(result1).toBe('home');
    expect(result2).toBe('home');
    expect(result3).toBe('home');
  });
});

describe('createStepRoutes', () => {
  it('creates a correct mapping with a single step', () => {
    const steps = ['profile'] as const;
    const result = createStepRoutes(steps);
    expect(result).toEqual({ profile: 'profile' });
  });

  it('creates a correct mapping with multiple steps', () => {
    const steps = ['profile', 'settings', 'billing'] as const;
    const result = createStepRoutes(steps);
    expect(result).toEqual({
      profile: 'profile',
      settings: 'settings',
      billing: 'billing'
    });
  });

  it('preserves the step order in the returned object', () => {
    const steps = ['step3', 'step1', 'step2'] as const;
    const result = createStepRoutes(steps);
    const keys = Object.keys(result);
    expect(keys).toEqual(['step3', 'step1', 'step2']);
  });

  it('returns an empty object for an empty array', () => {
    const steps = [] as const;
    const result = createStepRoutes(steps);
    expect(result).toEqual({});
  });

  it('handles steps with special characters and spaces', () => {
    const steps = [
      'step-with-dashes',
      'step_with_underscores',
      'step with spaces',
      'step@special#chars'
    ] as const;
    const result = createStepRoutes(steps);
    expect(result).toEqual({
      'step-with-dashes': 'step-with-dashes',
      step_with_underscores: 'step_with_underscores',
      'step with spaces': 'step with spaces',
      'step@special#chars': 'step@special#chars'
    });
  });

  it('handles duplicate step names correctly', () => {
    const steps = ['profile', 'settings', 'profile'] as const;
    const result = createStepRoutes(steps);
    expect(result).toEqual({
      profile: 'profile',
      settings: 'settings'
    });
    expect(Object.keys(result)).toHaveLength(2);
  });
});

describe('flattenValidationErrors', () => {
  it('flattens nested validation errors with parent keys', () => {
    const errors: ValidationErrorObject = {
      user: {
        name: {
          _errors: ['Name is required', 'Name must be at least 2 characters']
        },
        email: {
          _errors: ['Invalid email format']
        }
      }
    };

    const result = flattenValidationErrors(errors);

    expect(result).toEqual([
      'name: Name is required',
      'name: Name must be at least 2 characters',
      'email: Invalid email format'
    ]);
  });

  it('processes root level validation errors', () => {
    const errors: ValidationErrorObject = {
      _errors: ['Root level error 1', 'Root level error 2']
    };

    const result = flattenValidationErrors(errors);

    expect(result).toEqual(['Root level error 1', 'Root level error 2']);
  });

  it('combines errors from multiple nested levels', () => {
    const errors: ValidationErrorObject = {
      _errors: ['Root error'],
      level1: {
        _errors: ['Level 1 error'],
        level2: {
          _errors: ['Level 2 error'],
          level3: {
            _errors: ['Level 3 error']
          }
        }
      }
    };

    const result = flattenValidationErrors(errors);

    expect(result).toEqual([
      'Root error',
      'level1: Level 1 error',
      'level2: Level 2 error',
      'level3: Level 3 error'
    ]);
  });

  it('handles empty validation error object', () => {
    const errors: ValidationErrorObject = {};

    const result = flattenValidationErrors(errors);

    expect(result).toEqual([]);
  });

  it('processes validation object with null values', () => {
    const errors: ValidationErrorObject = {
      _errors: ['Root error'],
      nullField: null,
      undefinedField: undefined,
      validField: {
        _errors: ['Valid field error']
      }
    };

    const result = flattenValidationErrors(errors);

    expect(result).toEqual(['Root error', 'validField: Valid field error']);
  });

  it('test_handles_empty_errors_array', () => {
    const errors: ValidationErrorObject = {
      _errors: [],
      field: {
        _errors: []
      }
    };

    const result = flattenValidationErrors(errors);

    expect(result).toEqual([]);
  });

  it('test_flatten_validation_errors_with_parent_key_prefixes', () => {
    const errors: ValidationErrorObject = {
      name: {
        _errors: ['Name is required']
      },
      address: {
        street: {
          _errors: ['Street is required']
        }
      }
    };

    const result = flattenValidationErrors(errors, 'form');

    expect(result).toEqual([
      'name: Name is required',
      'street: Street is required'
    ]);
  });

  it('test_flatten_nested_validation_error_objects_recursively', () => {
    const errors: ValidationErrorObject = {
      user: {
        profile: {
          personal: {
            name: {
              _errors: ['Deep nested error']
            }
          }
        }
      }
    };

    const result = flattenValidationErrors(errors);

    expect(result).toEqual(['name: Deep nested error']);
  });

  it('test_flatten_validation_errors_without_parent_key', () => {
    const errors: ValidationErrorObject = {
      _errors: ['Error without parent key'],
      field: {
        _errors: ['Field error']
      }
    };

    const result = flattenValidationErrors(errors);

    expect(result).toEqual(['Error without parent key', 'field: Field error']);
  });

  it('test_flatten_empty_validation_error_object', () => {
    const errors: ValidationErrorObject = {};

    const result = flattenValidationErrors(errors);

    expect(result).toEqual([]);
  });

  it('test_flatten_validation_errors_with_null_nested_values', () => {
    const errors: ValidationErrorObject = {
      _errors: ['Root error'],
      nullValue: null,
      validNested: {
        _errors: ['Nested error']
      }
    };

    const result = flattenValidationErrors(errors);

    expect(result).toEqual(['Root error', 'validNested: Nested error']);
  });

  it('test_flatten_validation_errors_with_empty_errors_array', () => {
    const errors: ValidationErrorObject = {
      _errors: [],
      nested: {
        _errors: []
      }
    };

    const result = flattenValidationErrors(errors);

    expect(result).toEqual([]);
  });
});

describe('checkIsSafari', () => {
  it('returns true for Safari user agent', () => {
    mockWindow.navigator.userAgent =
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15';
    expect(checkIsSafari()).toBe(true);
  });

  it('returns false for Chrome user agent', () => {
    mockWindow.navigator.userAgent =
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
    expect(checkIsSafari()).toBe(false);
  });

  it('returns false for Firefox user agent', () => {
    mockWindow.navigator.userAgent =
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0';
    expect(checkIsSafari()).toBe(false);
  });

  it('returns false for Edge user agent', () => {
    mockWindow.navigator.userAgent =
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59';
    expect(checkIsSafari()).toBe(false);
  });

  it('returns false when window is undefined (SSR)', () => {
    vi.stubGlobal('window', undefined);
    expect(checkIsSafari()).toBe(false);
  });
});

describe('checkIsAndroid', () => {
  it('returns true for Android user agent', () => {
    mockWindow.navigator.userAgent =
      'Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Mobile Safari/537.36';
    expect(checkIsAndroid()).toBe(true);
  });

  it('returns false for iOS user agent', () => {
    mockWindow.navigator.userAgent =
      'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1';
    expect(checkIsAndroid()).toBe(false);
  });

  it('returns false for desktop user agent', () => {
    mockWindow.navigator.userAgent =
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
    expect(checkIsAndroid()).toBe(false);
  });

  it('returns false when window is undefined (SSR)', () => {
    vi.stubGlobal('window', undefined);
    expect(checkIsAndroid()).toBe(false);
  });
});

describe('checkIsIos', () => {
  it('returns true for iPhone user agent', () => {
    mockWindow.navigator.userAgent =
      'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1';
    expect(checkIsIos()).toBe(true);
  });

  it('returns true for iPad user agent', () => {
    mockWindow.navigator.userAgent =
      'Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1';
    expect(checkIsIos()).toBe(true);
  });

  it('returns true for iPod user agent', () => {
    mockWindow.navigator.userAgent =
      'Mozilla/5.0 (iPod touch; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1';
    expect(checkIsIos()).toBe(true);
  });

  it('returns false for Android user agent', () => {
    mockWindow.navigator.userAgent =
      'Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Mobile Safari/537.36';
    expect(checkIsIos()).toBe(false);
  });

  it('returns false for desktop user agent', () => {
    mockWindow.navigator.userAgent =
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
    expect(checkIsIos()).toBe(false);
  });

  it('returns false when window is undefined (SSR)', () => {
    vi.stubGlobal('window', undefined);
    expect(checkIsIos()).toBe(false);
  });
});

describe('checkIsDesktop', () => {
  it('returns true for desktop user agent', () => {
    mockWindow.navigator.userAgent =
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
    expect(checkIsDesktop()).toBe(true);
  });

  it('returns false for mobile user agent', () => {
    mockWindow.navigator.userAgent =
      'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1';
    expect(checkIsDesktop()).toBe(false);
  });

  it('returns false for Android mobile user agent', () => {
    mockWindow.navigator.userAgent =
      'Mozilla/5.0 (Linux; Android 10; SM-G973F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Mobile Safari/537.36';
    expect(checkIsDesktop()).toBe(false);
  });

  it('returns false when window is undefined (SSR)', () => {
    vi.stubGlobal('window', undefined);
    expect(checkIsDesktop()).toBe(false);
  });
});

describe('standariseNetworkError', () => {
  it('handles payment failed errors', () => {
    const error = {
      code: 'paymentFailed',
      message: 'Card declined'
    };
    const result = standariseNetworkError(error);
    expect(result).toEqual([
      'There was an issue with your payment and it did not complete. Card declined'
    ]);
  });

  it('handles network errors by message', () => {
    const networkErrors = [
      { message: 'Network Error' },
      { message: 'Request timeout' },
      { message: 'fetch failed' },
      { message: 'Failed to fetch' }
    ];

    networkErrors.forEach((error) => {
      const result = standariseNetworkError(error);
      expect(result).toEqual(['Network error occurred']);
    });
  });

  it('handles network errors by name', () => {
    const networkErrors = [{ name: 'NetworkError' }, { name: 'TypeError' }];

    networkErrors.forEach((error) => {
      const result = standariseNetworkError(error);
      expect(result).toEqual(['Network error occurred']);
    });
  });

  it('handles network errors by code', () => {
    const networkErrors = [{ code: 'NETWORK_ERROR' }, { code: 'ERR_NETWORK' }];

    networkErrors.forEach((error) => {
      const result = standariseNetworkError(error);
      expect(result).toEqual(['Network error occurred']);
    });
  });

  it('handles errors without response', () => {
    const error = { message: 'Some error' };
    const result = standariseNetworkError(error);
    expect(result).toEqual(['Network error occurred']);
  });

  it('handles response with error data', () => {
    const error = {
      response: {
        data: {
          error: 'Server error message'
        }
      }
    };
    const result = standariseNetworkError(error);
    expect(result).toEqual(['Server error message']);
  });

  it('handles response with message data', () => {
    const error = {
      response: {
        data: {
          message: 'API error message'
        }
      }
    };
    const result = standariseNetworkError(error);
    expect(result).toEqual(['API error message']);
  });

  it('returns default message for unknown errors', () => {
    const error = {
      response: {
        data: {}
      }
    };
    const result = standariseNetworkError(error);
    expect(result).toEqual(['Something went wrong, please try again later.']);
  });
});

describe('createTestId', () => {
  it('converts name to lowercase and replaces spaces with hyphens', () => {
    expect(createTestId('Submit Button')).toBe('submit-button');
    expect(createTestId('User Profile Form')).toBe('user-profile-form');
  });

  it('handles multiple spaces', () => {
    expect(createTestId('Multiple   Spaces   Here')).toBe(
      'multiple-spaces-here'
    );
  });

  it('handles already lowercase names', () => {
    expect(createTestId('already lowercase')).toBe('already-lowercase');
  });

  it('handles names with special characters', () => {
    expect(createTestId('Name With @#$ Special')).toBe('name-with-@#$-special');
  });

  it('returns empty string for empty input', () => {
    expect(createTestId('')).toBe('');
  });

  it('returns empty string for null/undefined input', () => {
    expect(createTestId(null as any)).toBe('');
    expect(createTestId(undefined as any)).toBe('');
  });
});

describe('getRemainingDays', () => {
  it('calculates remaining days correctly with Date objects', () => {
    const currentDate = new Date('2025-01-01');
    const endDate = new Date('2025-01-10');
    expect(getRemainingDays(endDate, currentDate)).toBe(9);
  });

  it('calculates remaining days correctly with date strings', () => {
    expect(getRemainingDays('2025-01-10', '2025-01-01')).toBe(9);
  });

  it('returns 0 for same dates', () => {
    const date = new Date('2025-01-01');
    expect(getRemainingDays(date, date)).toBe(0);
  });

  it('returns negative number for past dates', () => {
    expect(getRemainingDays('2025-01-01', '2025-01-10')).toBe(-9);
  });

  it('handles mixed Date and string inputs', () => {
    const endDate = new Date('2025-01-10');
    expect(getRemainingDays(endDate, '2025-01-01')).toBe(9);
    expect(getRemainingDays('2025-01-10', new Date('2025-01-01'))).toBe(9);
  });

  it('normalizes time to midnight for accurate day calculation', () => {
    const currentDate = new Date('2025-01-01T15:30:00');
    const endDate = new Date('2025-01-02T08:15:00');
    expect(getRemainingDays(endDate, currentDate)).toBe(1);
  });

  it('uses current date when no currentDate provided', () => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 5);
    expect(getRemainingDays(futureDate)).toBe(5);
  });
});

describe('constructRemainingDaysMessage', () => {
  it('returns null for 0 remaining days', () => {
    expect(constructRemainingDaysMessage(0)).toBeNull();
  });

  it('returns singular message for 1 day', () => {
    expect(constructRemainingDaysMessage(1)).toBe('1 day left');
  });

  it('returns plural message for multiple days', () => {
    expect(constructRemainingDaysMessage(5)).toBe('5 days left');
    expect(constructRemainingDaysMessage(30)).toBe('30 days left');
  });

  it('handles negative days', () => {
    expect(constructRemainingDaysMessage(-1)).toBe('-1 days left');
    expect(constructRemainingDaysMessage(-5)).toBe('-5 days left');
  });
});

describe('calculateTotalCost', () => {
  it('calculates total cost for single item', () => {
    const items: OrderItemWithQuantity[] = [
      {
        id: 1,
        name: 'Item 1',
        price: 10,
        quantity: 2,
        displayName: 'Item 1'
      }
    ];
    expect(calculateTotalCost(items)).toBe('20');
  });

  it('calculates total cost for multiple items', () => {
    const items: OrderItemWithQuantity[] = [
      {
        id: 1,
        name: 'Item 1',
        price: 10,
        quantity: 2,
        displayName: 'Item 1'
      },
      {
        id: 2,
        name: 'Item 2',
        price: 15,
        quantity: 1,
        displayName: 'Item 2'
      }
    ];
    expect(calculateTotalCost(items)).toBe('35');
  });

  it('returns 0 for empty array', () => {
    expect(calculateTotalCost([])).toBe('0');
  });

  it('handles decimal prices', () => {
    const items: OrderItemWithQuantity[] = [
      {
        id: 1,
        name: 'Item 1',
        price: 10.99,
        quantity: 2,
        displayName: 'Item 1'
      }
    ];
    expect(calculateTotalCost(items)).toBe('21.98');
  });
});

describe('getFieldError', () => {
  it('returns error message for matching field', () => {
    const errors = ['name: Name is required', 'email: Invalid email'];
    expect(getFieldError('name', errors)).toBe('Name is required');
    expect(getFieldError('email', errors)).toBe('Invalid email');
  });

  it('returns null for non-matching field', () => {
    const errors = ['name: Name is required', 'email: Invalid email'];
    expect(getFieldError('phone', errors)).toBeNull();
  });

  it('returns null for empty errors array', () => {
    expect(getFieldError('name', [])).toBeNull();
  });

  it('handles complex field names', () => {
    const errors = ['user.profile.name: Name is required'];
    expect(getFieldError('user.profile.name', errors)).toBe('Name is required');
  });
});

describe('hasAlreadyRestoredSignupStep', () => {
  it('returns true when signup step has been restored', () => {
    mockLocalStorage.getItem.mockReturnValue('1');
    expect(hasAlreadyRestoredSignupStep()).toBe(true);
    expect(mockLocalStorage.getItem).toHaveBeenCalledWith(
      LocalKey.SIGNUP_STEP_RESTORED
    );
  });

  it('returns false when signup step has not been restored', () => {
    mockLocalStorage.getItem.mockReturnValue(null);
    expect(hasAlreadyRestoredSignupStep()).toBe(false);
  });

  it('returns false when localStorage returns different value', () => {
    mockLocalStorage.getItem.mockReturnValue('0');
    expect(hasAlreadyRestoredSignupStep()).toBe(false);
  });
});

describe('isValidSignupPath', () => {
  it('returns true for valid signup paths', () => {
    // Based on the ROUTES_CONFIG from deliveroo, these are valid paths
    expect(isValidSignupPath('/signup/plan-selection')).toBe(true);
    expect(isValidSignupPath('/signup/number-porting')).toBe(true);
    expect(isValidSignupPath('/signup/payment')).toBe(true);
    expect(isValidSignupPath('/signup/register')).toBe(true);
  });

  it('returns false for invalid signup paths', () => {
    expect(isValidSignupPath('/invalid-path')).toBe(false);
    expect(isValidSignupPath('/some-random-path')).toBe(false);
    expect(isValidSignupPath('/not-in-config')).toBe(false);
  });

  it('returns true for dashboard paths that are in config', () => {
    expect(isValidSignupPath('/dashboard')).toBe(true);
    expect(isValidSignupPath('/dashboard/overview')).toBe(true);
  });

  it('handles empty string and special cases', () => {
    expect(isValidSignupPath('')).toBe(true); // sign-in path is empty string
    expect(isValidSignupPath('/login')).toBe(true);
  });
});

describe('shouldRestoreToSavedPath', () => {
  it('returns true when saved path exists and differs from current path', () => {
    expect(
      shouldRestoreToSavedPath(
        '/signup/plan-selection',
        '/signup/personal-details'
      )
    ).toBe(true);
  });

  it('returns false when saved path is same as current path', () => {
    expect(
      shouldRestoreToSavedPath(
        '/signup/plan-selection',
        '/signup/plan-selection'
      )
    ).toBe(false);
  });

  it('returns falsy when saved path is empty', () => {
    expect(shouldRestoreToSavedPath('', '/signup/plan-selection')).toBeFalsy();
  });

  it('returns falsy when saved path is null or undefined', () => {
    expect(
      shouldRestoreToSavedPath(null as any, '/signup/plan-selection')
    ).toBeFalsy();
    expect(
      shouldRestoreToSavedPath(undefined as any, '/signup/plan-selection')
    ).toBeFalsy();
  });
});

describe('markSignupStepAsRestored', () => {
  it('sets the restored flag in sessionStorage', () => {
    markSignupStepAsRestored();
    expect(mockSessionStorage.setItem).toHaveBeenCalledWith(
      LocalKey.SIGNUP_STEP_RESTORED,
      '1'
    );
  });
});

describe('getCurrentSignupStep', () => {
  it('returns parsed signup step from localStorage', () => {
    const mockStep = {
      brand: 'deliveroo',
      stepId: 'plan-selection',
      at: 1234567890
    };
    mockLocalStorage.getItem.mockReturnValue(JSON.stringify(mockStep));

    const result = getCurrentSignupStep();
    expect(result).toEqual(mockStep);
    expect(mockLocalStorage.getItem).toHaveBeenCalledWith(LocalKey.SIGNUP_STEP);
  });

  it('returns empty object when localStorage is empty', () => {
    mockLocalStorage.getItem.mockReturnValue('{}');
    const result = getCurrentSignupStep();
    expect(result).toEqual({});
  });

  it('returns empty object when localStorage contains null', () => {
    mockLocalStorage.getItem.mockReturnValue(null);
    const result = getCurrentSignupStep();
    expect(result).toEqual({});
  });

  it('handles JSON parse errors gracefully', () => {
    mockLocalStorage.getItem.mockReturnValue('invalid-json');
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    const result = getCurrentSignupStep();
    expect(result).toEqual({});
    expect(consoleSpy).toHaveBeenCalledWith(
      'Failed to parse signup step from localStorage'
    );

    consoleSpy.mockRestore();
  });
});

describe('extractCorrelationIdsFromBasket', () => {
  it('extracts correlation IDs from purchase details basket', () => {
    const mockDetails = {
      purchase_details: {
        basket: [
          { correlation_id: 'corr-1', item_id: 1, quantity: 1 },
          { correlation_id: 'corr-2', item_id: 2, quantity: 2 },
          { correlation_id: 'corr-3', item_id: 3, quantity: 1 }
        ]
      }
    };
    // @ts-expect-error test
    const result = extractCorrelationIdsFromBasket(mockDetails);
    expect(result).toEqual(['corr-1', 'corr-2', 'corr-3']);
  });

  it('returns empty array for empty basket', () => {
    const mockDetails = {
      purchase_details: {
        basket: []
      }
    };
    // @ts-expect-error test
    const result = extractCorrelationIdsFromBasket(mockDetails);
    expect(result).toEqual([]);
  });
});

describe('findSimsByCorrelationIds', () => {
  it('finds SIMs by correlation IDs', () => {
    const correlationIds = ['corr-1', 'corr-3'];
    const sims = [
      { correlation_id: 'corr-1', sim_id: 'sim-1', status: 'active' },
      { correlation_id: 'corr-2', sim_id: 'sim-2', status: 'inactive' },
      { correlation_id: 'corr-3', sim_id: 'sim-3', status: 'active' }
    ];
    // @ts-expect-error test
    const result = findSimsByCorrelationIds(correlationIds, sims);
    expect(result).toEqual([
      { correlation_id: 'corr-1', sim_id: 'sim-1', status: 'active' },
      { correlation_id: 'corr-3', sim_id: 'sim-3', status: 'active' }
    ]);
  });

  it('returns empty array when no matches found', () => {
    const correlationIds = ['corr-nonexistent'];
    const sims = [
      { correlation_id: 'corr-1', sim_id: 'sim-1', status: 'active' }
    ];
    // @ts-expect-error test
    const result = findSimsByCorrelationIds(correlationIds, sims);
    expect(result).toEqual([]);
  });

  it('handles empty correlation IDs array', () => {
    const correlationIds: string[] = [];
    const sims = [
      { correlation_id: 'corr-1', sim_id: 'sim-1', status: 'active' }
    ];
    // @ts-expect-error test
    const result = findSimsByCorrelationIds(correlationIds, sims);
    expect(result).toEqual([]);
  });

  it('handles empty sims array', () => {
    const correlationIds = ['corr-1'];
    const sims = [];

    const result = findSimsByCorrelationIds(correlationIds, sims);
    expect(result).toEqual([]);
  });
});

describe('filterSubscriptionsByCorrelationIds', () => {
  it('filters subscriptions by correlation IDs', () => {
    const correlationIds = ['corr-1', 'corr-3'];
    const subscriptions = [
      { correlation_id: 'corr-1', subscription_id: 'sub-1', status: 'active' },
      {
        correlation_id: 'corr-2',
        subscription_id: 'sub-2',
        status: 'inactive'
      },
      { correlation_id: 'corr-3', subscription_id: 'sub-3', status: 'active' },
      { correlation_id: null, subscription_id: 'sub-4', status: 'active' }
    ];

    const result = filterSubscriptionsByCorrelationIds(
      correlationIds,
      // @ts-expect-error test
      subscriptions
    );
    expect(result).toEqual([
      { correlation_id: 'corr-1', subscription_id: 'sub-1', status: 'active' },
      { correlation_id: 'corr-3', subscription_id: 'sub-3', status: 'active' }
    ]);
  });

  it('excludes subscriptions with null correlation_id', () => {
    const correlationIds = ['corr-1'];
    const subscriptions = [
      { correlation_id: 'corr-1', subscription_id: 'sub-1', status: 'active' },
      { correlation_id: null, subscription_id: 'sub-2', status: 'active' }
    ];

    const result = filterSubscriptionsByCorrelationIds(
      correlationIds,
      // @ts-expect-error test
      subscriptions
    );
    expect(result).toEqual([
      { correlation_id: 'corr-1', subscription_id: 'sub-1', status: 'active' }
    ]);
  });

  it('returns empty array when no matches found', () => {
    const correlationIds = ['corr-nonexistent'];
    const subscriptions = [
      { correlation_id: 'corr-1', subscription_id: 'sub-1', status: 'active' }
    ];

    const result = filterSubscriptionsByCorrelationIds(
      correlationIds,
      // @ts-expect-error test
      subscriptions
    );
    expect(result).toEqual([]);
  });

  it('handles empty correlation IDs array', () => {
    const correlationIds: string[] = [];
    const subscriptions = [
      { correlation_id: 'corr-1', subscription_id: 'sub-1', status: 'active' }
    ];

    const result = filterSubscriptionsByCorrelationIds(
      correlationIds,
      // @ts-expect-error test
      subscriptions
    );
    expect(result).toEqual([]);
  });

  it('handles empty subscriptions array', () => {
    const correlationIds = ['corr-1'];
    const subscriptions = [];

    const result = filterSubscriptionsByCorrelationIds(
      correlationIds,
      subscriptions
    );
    expect(result).toEqual([]);
  });
});

describe('getDayMonthYearFromDate', () => {
  it('parses valid date string', () => {
    expect(getDayMonthYearFromDate('2024-06-01')).toEqual({
      year: '2024',
      month: '06',
      day: '01'
    });
  });
  it('handles invalid date string', () => {
    expect(getDayMonthYearFromDate('2024-06')).toEqual({
      year: '2024',
      month: '06',
      day: undefined
    });
  });
  it('handles empty string', () => {
    expect(getDayMonthYearFromDate('')).toEqual({
      year: '',
      month: '',
      day: ''
    });
  });
});

describe('filterActionGroups', () => {
  type TestAction = {
    buttonText: string;
    modalTitle?: string;
    component?: any;
  };
  type TestGroup = { title: string; actions: TestAction[] };

  const noop = () => null;

  const buildGroups = (): TestGroup[] => [
    {
      title: 'Subscriptions',
      actions: [
        { buttonText: 'Cancel plan', modalTitle: 'Cancel', component: noop },
        {
          buttonText: 'Resume subscription',
          modalTitle: 'Resume',
          component: noop
        },
        { buttonText: 'Something else', modalTitle: 'Other', component: noop }
      ]
    },
    {
      title: 'Billing',
      actions: [
        { buttonText: 'Change card', modalTitle: 'Card', component: noop }
      ]
    }
  ];

  it('removes the Subscriptions group entirely when subscription is cancelled', () => {
    const groups = buildGroups();
    const result = filterActionGroups(true, false, groups as any);

    expect(result.some((g: any) => g.title === 'Subscriptions')).toBe(false);
    expect(result).toHaveLength(1);
    expect(result[0].title).toBe('Billing');
  });

  it('keeps only the "Cancel plan" action when cancellation is not pending', () => {
    const groups = buildGroups();
    const result = filterActionGroups(false, false, groups as any);

    const subs = result.find((g: any) => g.title === 'Subscriptions');
    expect(subs).toBeDefined();
    expect(subs!.actions).toHaveLength(1);
    expect(subs!.actions[0].buttonText).toBe('Cancel plan');

    const billing = result.find((g: any) => g.title === 'Billing');
    expect(billing?.actions[0].buttonText).toBe('Change card');
  });

  it('keeps only the "Resume subscription" action when cancellation is pending', () => {
    const groups = buildGroups();
    const result = filterActionGroups(false, true, groups as any);

    const subs = result.find((g: any) => g.title === 'Subscriptions');
    expect(subs).toBeDefined();
    expect(subs!.actions).toHaveLength(1);
    expect(subs!.actions[0].buttonText).toBe('Resume subscription');
  });

  it('returns empty actions if Subscriptions group lacks expected actions', () => {
    const groups: TestGroup[] = [
      {
        title: 'Subscriptions',
        actions: [{ buttonText: 'Unknown', component: noop }]
      },
      {
        title: 'Billing',
        actions: [{ buttonText: 'Change card', component: noop }]
      }
    ];

    const resultNotPending = filterActionGroups(false, false, groups as any);
    const subsNotPending = resultNotPending.find(
      (g: any) => g.title === 'Subscriptions'
    );
    expect(subsNotPending?.actions).toHaveLength(0);

    const resultPending = filterActionGroups(false, true, groups as any);
    const subsPending = resultPending.find(
      (g: any) => g.title === 'Subscriptions'
    );
    expect(subsPending?.actions).toHaveLength(0);
  });

  it('returns the same groups when there is no Subscriptions group', () => {
    const groups: TestGroup[] = [
      {
        title: 'Billing',
        actions: [{ buttonText: 'Change card', component: noop }]
      },
      {
        title: 'Help',
        actions: [{ buttonText: 'Contact support', component: noop }]
      }
    ];

    const result = filterActionGroups(false, false, groups as any);
    expect(result).toEqual(groups);
  });

  it('with multiple Subscriptions groups: when cancelled, removes only the first Subscriptions group', () => {
    const groups: TestGroup[] = [
      {
        title: 'Subscriptions',
        actions: [{ buttonText: 'Cancel plan', component: noop }]
      },
      {
        title: 'Billing',
        actions: [{ buttonText: 'Change card', component: noop }]
      },
      {
        title: 'Subscriptions',
        actions: [{ buttonText: 'Resume subscription', component: noop }]
      }
    ];

    const result = filterActionGroups(true, false, groups as any);
    const subscriptionGroups = result.filter(
      (g: any) => g.title === 'Subscriptions'
    );
    // First one removed, second remains
    expect(subscriptionGroups).toHaveLength(1);
    expect(subscriptionGroups[0].actions[0].buttonText).toBe(
      'Resume subscription'
    );
  });
});
