import {
  MainPlan,
  SimsApiResponse,
  SubscriptionsApiResponse
} from '@/schemas/schemas';
import type { BasketPlan } from '@/src/deliveroo/app/signup/_reducer/reducer';
import { calculateTotalCost, OrderItemWithQuantity } from '@/utils/helpers';
import { ORDER_CONFIRMATION_CONFIG } from '@/src/deliveroo/app/signup/payment/_constants/constants';
import type { DataState } from '@/src/deliveroo/app/signup/payment/order-confirmation/types';
import { PurchaseIntentDetails } from '@/services/paymentService';
import {
  ActionGroup,
  ModalConfig
} from '@/src/deliveroo/app/dashboard/esim/@yourplans/page';

export interface DeliverooSelectionState {
  mainPlanQuantity: number;
  mainPlanId: number | string;
}

export function deliverooSelectionStateToMainPlans(
  payload: DeliverooSelectionState | null | undefined
): { mainPlans: BackendPlan[] } {
  if (!payload) {
    return { mainPlans: [] };
  }

  return {
    mainPlans: Array.from({ length: payload.mainPlanQuantity || 0 }, () => ({
      id: String(payload.mainPlanId)
    }))
  };
}

export interface BackendPlan {
  id: string | number;
}

export type BasketSummaryItem = {
  planId: number;
  planName: string;
  planPrice: number;
  quantity: number;
  totalPrice: number;
  displayName: string;
  europeData: number;
};

export type OrderSummary = {
  orderItems: OrderItemWithQuantity[];
  totalCost: string;
  basketSummary: BasketSummaryItem[];
};

export function createBasketSummaryItems(
  basketPlans: BasketPlan[],
  planData: MainPlan[] | undefined
): BasketSummaryItem[] {
  if (!planData || basketPlans.length === 0) return [];

  const quantity = basketPlans.length;
  const firstPlanId = basketPlans[0]?.planId;
  if (firstPlanId === undefined || firstPlanId === null) return [];

  const plan = planData.find((plan) => String(plan.id) === String(firstPlanId));

  const planPrice = plan?.price ?? 0;
  const planName = plan?.name ?? `Plan ${firstPlanId}`;
  const totalPrice = planPrice * quantity;

  return [
    {
      planId: Number(firstPlanId),
      planName,
      planPrice,
      quantity,
      totalPrice,
      displayName: planName,
      europeData: plan?.allowances.europe_data ?? 0
    }
  ];
}

export function createOrderItemsFromBasket(
  basketPlans: BasketPlan[],
  planData: MainPlan[] | undefined
): OrderItemWithQuantity[] {
  const summaryItems = createBasketSummaryItems(basketPlans, planData);

  return summaryItems.map((item) => ({
    id: Number(item.planId),
    name: item.displayName,
    price: item.planPrice,
    quantity: item.quantity,
    displayName: item.displayName
  }));
}

export function createOrderSummary(
  basketPlans: BasketPlan[],
  planData: MainPlan[] | undefined
): OrderSummary {
  const orderItems = createOrderItemsFromBasket(basketPlans, planData);
  const totalCost = calculateTotalCost(orderItems);

  return {
    orderItems,
    totalCost,
    basketSummary: createBasketSummaryItems(basketPlans, planData)
  };
}

export function generatePlanInstanceList(
  basketSummary: BasketSummaryItem[]
): (BasketSummaryItem & { instanceId: string })[] {
  return basketSummary.flatMap((item) =>
    Array.from({ length: item.quantity }, (_, index) => ({
      ...item,
      instanceId: `${item.planId}_${index}`
    }))
  );
}

// ORDER CONFIRMATION
// todo - tests !!!!

export interface ESimState {
  eSims: SimsApiResponse;
  expectedEsimCount: number;
  esimAttemptCount: number;
  isPolling: boolean;
}

export interface SubscriptionState {
  subscriptions: SubscriptionsApiResponse;
  subscriptionError: Error | null;
}

export interface LoadingState {
  isPolling: boolean;
  isSubscriptionPolling: boolean;
  fetchingPurchaseDetails: boolean;
}

export interface ErrorState {
  instructionError: Error | null;
  subscriptionError: Error | null;
}

export function computeDataState(state: {
  eSims: SimsApiResponse;
  subscriptions: SubscriptionsApiResponse;
  expectedEsimCount: number;
  esimAttemptCount: number;
  isPolling: boolean;
  isSubscriptionPolling: boolean;
  fetchingPurchaseDetails: boolean;
  instructionError: Error | null;
  subscriptionError: Error | null;
}): DataState {
  const eSimState: ESimState = {
    eSims: state.eSims,
    expectedEsimCount: state.expectedEsimCount,
    esimAttemptCount: state.esimAttemptCount,
    isPolling: state.isPolling
  };

  const subscriptionState: SubscriptionState = {
    subscriptions: state.subscriptions,
    subscriptionError: state.subscriptionError
  };

  const loadingState: LoadingState = {
    isPolling: state.isPolling,
    isSubscriptionPolling: state.isSubscriptionPolling,
    fetchingPurchaseDetails: state.fetchingPurchaseDetails
  };

  const errorState: ErrorState = {
    instructionError: state.instructionError,
    subscriptionError: state.subscriptionError
  };

  return {
    isReady:
      areSubscriptionsReady(subscriptionState) &&
      !state.fetchingPurchaseDetails,
    isLoading: isAnyOperationInProgress(loadingState),
    error: determineActiveError(errorState, eSimState),
    subscriptionsReady: areSubscriptionsReady(subscriptionState),
    simsReady: areESimsReady(eSimState, state.instructionError),
    simPollingTimedOut: hasESimPollingTimedOut(eSimState)
  };
}

export function hasValidESimData(eSims: SimsApiResponse): boolean {
  return eSims.length > 0 && isEsimInstallable(eSims[0]);
}

export function hasValidSubscriptions(
  subscriptions: SubscriptionsApiResponse
): boolean {
  return subscriptions.length > 0;
}

export function isAnyOperationInProgress(loadingState: LoadingState): boolean {
  return (
    loadingState.isPolling ||
    loadingState.isSubscriptionPolling ||
    loadingState.fetchingPurchaseDetails
  );
}

export function areSubscriptionsReady(
  subscriptionState: SubscriptionState
): boolean {
  return (
    hasValidSubscriptions(subscriptionState.subscriptions) &&
    !subscriptionState.subscriptionError
  );
}

export function areESimsReady(
  eSimState: ESimState,
  instructionError: Error | null
): boolean {
  return hasValidESimData(eSimState.eSims) && !instructionError;
}

export function hasESimPollingTimedOut(eSimState: ESimState): boolean {
  return hasPollingStoppedWithoutESimsWhenExpected(eSimState);
}

function hasPollingStoppedWithoutESimsWhenExpected(
  eSimState: ESimState
): boolean {
  return (
    !eSimState.isPolling &&
    hasReachedPollingAttemptLimit(eSimState.esimAttemptCount) &&
    !areESimsReady(eSimState, null) &&
    areESimsExpected(eSimState.expectedEsimCount)
  );
}

function hasReachedPollingAttemptLimit(attemptCount: number): boolean {
  return attemptCount >= ORDER_CONFIRMATION_CONFIG.STOP_POLLING_ON_ATTEMPT;
}

function areESimsExpected(expectedCount: number): boolean {
  return expectedCount > 0;
}

export function determineActiveError(
  errorState: ErrorState,
  eSimState: ESimState
): Error | null {
  if (errorState.instructionError) return errorState.instructionError;
  if (errorState.subscriptionError) return errorState.subscriptionError;
  if (hasESimPollingTimedOut(eSimState)) return createESimTimeoutError();
  return null;
}

function createESimTimeoutError(): Error {
  return new Error(
    'eSIM generation is taking longer than expected. We will email your QR code when ready.'
  );
}

function isEsimInstallable(esim: SimsApiResponse[number]) {
  if (!esim?.esim_data) return false;

  if (esim.status !== 'active') return false;

  const { esim_data } = esim;
  return Boolean(
    esim_data.qr_code_base64 &&
      esim_data.activation_code &&
      esim_data.qr_code_image &&
      esim_data.ios_universal_link &&
      esim_data.android_activation_data
  );
}

export interface PlanSectionProps {
  esimData: SimsApiResponse[number]['esim_data'] | undefined;
  dataState: DataState;
  totalCost: string;
}

export function buildPlanSectionProps(
  eSims: SimsApiResponse,
  dataState: DataState,
  purchaseDetails?: PurchaseIntentDetails
): PlanSectionProps {
  return {
    esimData: dataState.simsReady ? eSims[0]?.esim_data : undefined,
    dataState,
    totalCost: purchaseDetails?.order_summary.total_cost ?? ''
  };
}

export function getButtonText(
  shouldShowPendingState: boolean,
  isUniversalLinkUnavailable: boolean
): string {
  let buttonText: string;
  if (shouldShowPendingState) {
    buttonText = 'Generating eSIM...';
  } else if (isUniversalLinkUnavailable) {
    buttonText = 'Installation temporarily unavailable';
  } else {
    buttonText = 'Install eSIM';
  }

  return buttonText;
}

// export function filterActionGroups(
//   isSubscriptionCancelled: boolean,
//   isSubscriptionCancellationPending: boolean,
//   actionGroups: ActionGroup[]
// ): ActionGroup[] {
//   if (isSubscriptionCancelled) {
//     return removeSubscriptionsGroup(actionGroups);
//   }
//
//   return adjustSubscriptionActionsBasedOnCancellationStatus(
//     actionGroups,
//     isSubscriptionCancellationPending
//   );
// }

type ActionGroupTransformer = (groups: ActionGroup[]) => ActionGroup[];

export function createSubscriptionFilter(
  isCancelled: boolean,
  isPending: boolean
): ActionGroupTransformer {
  if (isCancelled) return removeSubscriptionsGroup;

  return (groups) =>
    adjustSubscriptionActionsBasedOnCancellationStatus(groups, isPending);
}

function removeSubscriptionsGroup(actionGroups: ActionGroup[]): ActionGroup[] {
  return actionGroups.filter(
    (group, index) => !isSubscriptionsGroupAtIndex(actionGroups, group, index)
  );
}

function isSubscriptionsGroupAtIndex(
  actionGroups: ActionGroup[],
  group: ActionGroup,
  currentIndex: number
): boolean {
  const subscriptionsGroupIndex = findSubscriptionsGroupIndex(actionGroups);
  return (
    isSubscriptionsGroup(group) && currentIndex === subscriptionsGroupIndex
  );
}

function findSubscriptionsGroupIndex(actionGroups: ActionGroup[]): number {
  return actionGroups.findIndex(isSubscriptionsGroup);
}

function isSubscriptionsGroup(group: ActionGroup): boolean {
  return group.title === 'Subscriptions';
}

function adjustSubscriptionActionsBasedOnCancellationStatus(
  actionGroups: ActionGroup[],
  isSubscriptionCancellationPending: boolean
): ActionGroup[] {
  return actionGroups.map((group) =>
    transformGroupIfSubscriptions(group, isSubscriptionCancellationPending)
  );
}

function transformGroupIfSubscriptions(
  group: ActionGroup,
  isSubscriptionCancellationPending: boolean
): ActionGroup {
  if (!isSubscriptionsGroup(group)) {
    return group;
  }

  return createGroupWithFilteredActions(
    group,
    isSubscriptionCancellationPending
  );
}

function createGroupWithFilteredActions(
  group: ActionGroup,
  isSubscriptionCancellationPending: boolean
): ActionGroup {
  const filteredActions = filterSubscriptionActions(
    group.actions,
    isSubscriptionCancellationPending
  );

  return { ...group, actions: filteredActions };
}

function filterSubscriptionActions(
  actions: ModalConfig[],
  isSubscriptionCancellationPending: boolean
): ModalConfig[] {
  return actions.filter((action) =>
    shouldIncludeSubscriptionAction(action, isSubscriptionCancellationPending)
  );
}

function shouldIncludeSubscriptionAction(
  action: ModalConfig,
  isSubscriptionCancellationPending: boolean
): boolean {
  if (isSubscriptionCancellationPending) {
    return isResumeSubscriptionAction(action);
  }
  return isCancelPlanAction(action);
}

function isCancelPlanAction(action: ModalConfig): boolean {
  return action.buttonText === 'Cancel plan';
}

function isResumeSubscriptionAction(action: ModalConfig): boolean {
  return action.buttonText === 'Resume subscription';
}
