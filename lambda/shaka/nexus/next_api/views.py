from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.shortcuts import get_object_or_404
from django.http import Http404
from core.models import Plan, Client, Subscription, Sim, PlanChange
from .authentication import NextApiBearerTokenAuthentication, validate_client_access, get_subscriber_from_request
from .serializers import PlanSerializer, NextApiSimSerializer, NextApiSubscriptionSerializer, SimActivationSerializer, NextMigrationSerializer


class PlansListView(APIView):
    permission_classes = [NextApiBearerTokenAuthentication]

    def get(self, request, client_id):
        try:
            client = validate_client_access(request, client_id)

            plans = Plan.objects.filter(
                client=client,
                status=Plan.PlanStatuses.ACTIVE
            ).prefetch_related(
                'family_plan_links__sub_plan',
                'travel_addons',
                'plantraveladdon_set__travel_addon__roaming_esim_packages'
            ).order_by('name')

            serializer = PlanSerializer(plans, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as exc:  # pylint: disable=broad-exception-caught
            return Response(
                {'error': str(exc)},
                status=status.HTTP_400_BAD_REQUEST
            )


class PlanDetailView(APIView):
    permission_classes = [NextApiBearerTokenAuthentication]

    def get(self, request, client_id, plan_id):
        try:
            client = validate_client_access(request, client_id)

            plan = get_object_or_404(
                Plan.objects.prefetch_related(
                    'family_plan_links__sub_plan',
                    'travel_addons',
                    'plantraveladdon_set__travel_addon__roaming_esim_packages'
                ),
                id=plan_id,
                client=client,
                status=Plan.PlanStatuses.ACTIVE
            )
            serializer = PlanSerializer(plan)

            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as exc:  # pylint: disable=broad-exception-caught
            return Response(
                {'error': str(exc)},
                status=status.HTTP_400_BAD_REQUEST
            )


class ClientPlansView(APIView):
    def get(self, request, client_id):
        try:
            lookup = {'pk': int(client_id), 'obfuscated_id':''}
        except ValueError:
            lookup = {'obfuscated_id': client_id}
        client = Client.objects.get(**lookup)
        try:
            plans = Plan.objects.filter(
                client=client,
                status=Plan.PlanStatuses.ACTIVE
            ).prefetch_related(
                'family_plan_links__sub_plan',
                'travel_addons',
                'plantraveladdon_set__travel_addon__roaming_esim_packages'
            ).order_by('name')
            serializer = PlanSerializer(plans, many=True)

            return Response(serializer.data, status=status.HTTP_200_OK)

        except Exception as exc:  # pylint: disable=broad-exception-caught
            return Response(
                {'error': str(exc)},
                status=status.HTTP_400_BAD_REQUEST
            )


class HealthCheckView(APIView):
    def get(self, request):
        return Response(
            {
                'status': 'healthy',
                'service': 'next_api',
                'version': '1.0.0'
            },
            status=status.HTTP_200_OK
        )


class SubscriptionNameUpdateView(APIView):
    permission_classes = [NextApiBearerTokenAuthentication]

    def post(self, request, client_id, subscription_id):
        try:
            client = validate_client_access(request, client_id)
            subscriber = get_subscriber_from_request(request)

            name = request.data.get('name', '').strip()

            if len(name) > 20:
                return Response(
                    {'error': 'Subscription name cannot exceed 20 characters'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            try:
                subscription = Subscription.objects.get(
                    id=subscription_id,
                    subscriber=subscriber,
                    subscriber__client=client
                )
            except Subscription.DoesNotExist:
                return Response(
                    {'error': 'Subscription not found or access denied'},
                    status=status.HTTP_404_NOT_FOUND
                )

            subscription.user_subscription_name = name
            subscription.save(update_fields=['user_subscription_name'])

            return Response(
                {
                    'success': True,
                    'subscription_id': subscription_id,
                    'name': name
                },
                status=status.HTTP_200_OK
            )

        except Exception as exc:  # pylint: disable=broad-exception-caught
            return Response(
                {'error': str(exc)},
                status=status.HTTP_400_BAD_REQUEST
            )


class SubscriptionsListView(APIView):
    permission_classes = [NextApiBearerTokenAuthentication]

    def get(self, request, client_id):
        client = validate_client_access(request, client_id)
        subscriber = get_subscriber_from_request(request)

        subscriptions = Subscription.objects.filter(
            subscriber__client=client,
            subscriber=subscriber
        ).select_related('subscriber', 'intended_plan')

        serializer = NextApiSubscriptionSerializer(subscriptions, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

class SimsListView(APIView):
    permission_classes = [NextApiBearerTokenAuthentication]

    def get(self, request, client_id):
        client = validate_client_access(request, client_id)
        subscriber = get_subscriber_from_request(request)

        sims = Sim.objects.filter(
            subscription_assignments__subscription__subscriber__client=client,
            subscription_assignments__subscription__subscriber=subscriber
        ).prefetch_related(
            'subscription_assignments__subscription',
            'number_assignments',
            'plan_assignments__plan'
        ).distinct()

        serializer = NextApiSimSerializer(sims, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

class SimWaitForActivationView(APIView):
    permission_classes = [NextApiBearerTokenAuthentication]

    def get(self, request, _):
        try:
            raise RuntimeError()
            # sim = get_object_or_404(Sim, id=sim_id)

            # timeout_seconds = 300
            # start_time = time.time()

            # while time.time() - start_time < timeout_seconds:
            #     sim.refresh_from_db()

            #     if sim.is_active:
            #         break

            #     time.sleep(1)

            serializer = SimActivationSerializer(sim)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Http404:
            raise
            # serializer = SimActivationSerializer(sim)
            # return Response(serializer.data, status=status.HTTP_200_OK)
        except Exception as exc:  # pylint: disable=broad-exception-caught
            return Response(
                {'error': str(exc)},
                status=status.HTTP_400_BAD_REQUEST
            )


class MigrateSubscriptionView(APIView):
    permission_classes = [NextApiBearerTokenAuthentication]

    def post(self, request, client_id):
        client = validate_client_access(request, client_id)
        subscriber = get_subscriber_from_request(request)
        subscription = Subscription.objects.filter(
            subscriber__client=client,
            subscriber=subscriber,
            id=request.data.get('subscription_id')
        ).first()
        target_plan_id = request.data.get('target_plan_id')
        if target_plan_id:
            return Response({'error': 'Plan migration is not supported yet'}, status=status.HTTP_400_BAD_REQUEST)
        scheduled_date = request.data.get('scheduled_date')
        if scheduled_date:
            return Response({'error': 'Scheduled migration is not supported yet'}, status=status.HTTP_400_BAD_REQUEST)
        webhook_url = request.data.get('webhook_url')
        if webhook_url:
            return Response({'error': 'Webhooks are not supported yet'}, status=status.HTTP_400_BAD_REQUEST)
        if 'target_plan_id' in request.data and target_plan_id is None:
            pc = PlanChange.objects.create(change_type=PlanChange.ChangeType.CANCELLATION, subscription=subscription)
            return Response(NextMigrationSerializer(pc).data, status=status.HTTP_200_OK)
        return Response({'error': 'Invalid migration request'}, status=status.HTTP_400_BAD_REQUEST)


class CancelSubscriptionMigrationView(APIView):
    def delete(self, request, client_id, migration_id):
        client = validate_client_access(request, client_id)
        subscriber = get_subscriber_from_request(request)

        plan_change = PlanChange.objects.filter(
            subscription__subscriber=subscriber,
            change_type=PlanChange.ChangeType.CANCELLATION,
            pk=migration_id
        ).active().not_cancelled().first()

        if not plan_change:
            return Response(
                {'error': 'No migration found for this id'},
                status=status.HTTP_400_BAD_REQUEST
            )

        PlanChange.objects.create(target_plan_change=plan_change, subscription=plan_change.subscription, change_type=PlanChange.ChangeType.CANCEL_PLAN_CHANGE)

        return Response(
            {'success': True, 'message': 'Subscription migration has been cancelled'},
            status=status.HTTP_200_OK
        )
